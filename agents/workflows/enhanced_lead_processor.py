#!/usr/bin/env python3
"""
Enhanced Lead Processing Automation System

This module provides comprehensive lead processing including:
- Advanced lead scoring with multiple criteria
- Intelligent qualification based on market data
- Automated follow-up sequences with personalization
- Property analysis integration
- Lead nurturing workflows
"""

import os
import logging
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from agents.agent_base import AgentBase
from agents.lead_scoring_bot import LeadScoringBot
from agents.workflows.tier_classifier import classify_lead_tier
from agents.workflows.comping_workflow import run_comping_workflow
from agents.mao_calculator import MAOCalculator
from agents.followup_bot import FollowUpBot
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LeadStatus(Enum):
    """Lead status enumeration"""
    NEW = "new"
    QUALIFIED = "qualified"
    ANALYZING = "analyzing"
    OFFER_READY = "offer_ready"
    OFFER_SENT = "offer_sent"
    NEGOTIATING = "negotiating"
    NURTURING = "nurturing"
    CLOSED = "closed"
    DISQUALIFIED = "disqualified"

class LeadPriority(Enum):
    """Lead priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class LeadScore:
    """Lead scoring result"""
    total_score: float
    tier: int
    priority: LeadPriority
    confidence: float
    factors: Dict[str, float]
    recommendations: List[str]
    next_actions: List[str]

@dataclass
class QualificationResult:
    """Lead qualification result"""
    qualified: bool
    qualification_score: float
    disqualification_reasons: List[str]
    qualification_factors: Dict[str, Any]
    recommended_actions: List[str]

class EnhancedLeadProcessor(AgentBase):
    """
    Enhanced lead processor with advanced scoring, qualification, and automation
    """

    def __init__(self, lead_id: str, entity: str = "lead"):
        super().__init__(agent_name="enhanced_lead_processor", lead_id=lead_id, entity=entity)
        self.lead_data = getattr(self, 'context', {}).get("lead", {})
        self.scoring_weights = self._load_scoring_weights()
        self.qualification_criteria = self._load_qualification_criteria()

    def _load_scoring_weights(self) -> Dict[str, float]:
        """Load scoring weights from knowledge base or defaults"""
        try:
            weights_context = query_kb("What are the lead scoring weights and criteria?")
            # Parse weights from context or use defaults
            return {
                "property_value": 0.25,
                "equity_amount": 0.20,
                "motivation_level": 0.20,
                "timeline_urgency": 0.15,
                "property_condition": 0.10,
                "location_desirability": 0.10
            }
        except Exception as e:
            logger.warning(f"Could not load scoring weights: {e}, using defaults")
            return {
                "property_value": 0.25,
                "equity_amount": 0.20,
                "motivation_level": 0.20,
                "timeline_urgency": 0.15,
                "property_condition": 0.10,
                "location_desirability": 0.10
            }

    def _load_qualification_criteria(self) -> Dict[str, Any]:
        """Load qualification criteria from knowledge base or defaults"""
        try:
            criteria_context = query_kb("What are the lead qualification criteria and thresholds?")
            # Parse criteria from context or use defaults
            return {
                "min_equity": 50000,
                "max_property_value": 500000,
                "min_property_value": 100000,
                "required_motivation_indicators": [
                    "foreclosure", "divorce", "relocation", "inherited",
                    "behind on payments", "tax liens", "tired landlord"
                ],
                "disqualifying_factors": [
                    "not motivated", "just curious", "testing market",
                    "overpriced", "no equity", "retail condition"
                ]
            }
        except Exception as e:
            logger.warning(f"Could not load qualification criteria: {e}, using defaults")
            return {
                "min_equity": 50000,
                "max_property_value": 500000,
                "min_property_value": 100000,
                "required_motivation_indicators": [
                    "foreclosure", "divorce", "relocation", "inherited",
                    "behind on payments", "tax liens", "tired landlord"
                ],
                "disqualifying_factors": [
                    "not motivated", "just curious", "testing market",
                    "overpriced", "no equity", "retail condition"
                ]
            }

    def calculate_advanced_score(self) -> LeadScore:
        """
        Calculate advanced lead score using multiple weighted factors

        Returns:
            LeadScore: Comprehensive scoring result
        """
        factors = {}
        total_score = 0.0

        # 1. Property Value Score (0-100)
        property_value = self._extract_numeric_value(self.lead_data.get("property_value", 0))
        if property_value > 0:
            # Optimal range: $150k - $400k
            if 150000 <= property_value <= 400000:
                value_score = 100
            elif property_value < 150000:
                value_score = max(0, (property_value / 150000) * 80)
            else:
                # Diminishing returns for higher values
                value_score = max(20, 100 - ((property_value - 400000) / 10000))
        else:
            value_score = 0

        factors["property_value"] = value_score
        total_score += value_score * self.scoring_weights["property_value"]

        # 2. Equity Amount Score (0-100)
        equity = self._extract_numeric_value(self.lead_data.get("equity", 0))
        if equity >= 100000:
            equity_score = 100
        elif equity >= 50000:
            equity_score = 80
        elif equity >= 25000:
            equity_score = 60
        elif equity > 0:
            equity_score = 40
        else:
            equity_score = 0

        factors["equity_amount"] = equity_score
        total_score += equity_score * self.scoring_weights["equity_amount"]

        # 3. Motivation Level Score (0-100)
        motivation_score = self._calculate_motivation_score()
        factors["motivation_level"] = motivation_score
        total_score += motivation_score * self.scoring_weights["motivation_level"]

        # 4. Timeline Urgency Score (0-100)
        timeline_score = self._calculate_timeline_score()
        factors["timeline_urgency"] = timeline_score
        total_score += timeline_score * self.scoring_weights["timeline_urgency"]

        # 5. Property Condition Score (0-100)
        condition_score = self._calculate_condition_score()
        factors["property_condition"] = condition_score
        total_score += condition_score * self.scoring_weights["property_condition"]

        # 6. Location Desirability Score (0-100)
        location_score = self._calculate_location_score()
        factors["location_desirability"] = location_score
        total_score += location_score * self.scoring_weights["location_desirability"]

        # Determine tier and priority
        tier = 1 if total_score >= 70 else 2

        if total_score >= 85:
            priority = LeadPriority.CRITICAL
        elif total_score >= 70:
            priority = LeadPriority.HIGH
        elif total_score >= 50:
            priority = LeadPriority.MEDIUM
        else:
            priority = LeadPriority.LOW

        # Calculate confidence based on data completeness
        confidence = self._calculate_confidence()

        # Generate recommendations
        recommendations = self._generate_recommendations(total_score, factors)

        # Generate next actions
        next_actions = self._generate_next_actions(tier, priority, total_score)

        return LeadScore(
            total_score=total_score,
            tier=tier,
            priority=priority,
            confidence=confidence,
            factors=factors,
            recommendations=recommendations,
            next_actions=next_actions
        )

    def _calculate_motivation_score(self) -> float:
        """Calculate motivation score based on text analysis"""
        # Combine text fields
        text_fields = [
            self.lead_data.get("motivation", ""),
            self.lead_data.get("situation", ""),
            self.lead_data.get("notes", ""),
            self.lead_data.get("comments", "")
        ]
        combined_text = " ".join(text_fields).lower()

        # High motivation indicators
        high_motivation = [
            "foreclosure", "divorce", "bankruptcy", "job loss", "relocation",
            "inherited", "behind on payments", "tax liens", "need to sell fast",
            "motivated seller", "urgent", "asap", "immediately"
        ]

        # Medium motivation indicators
        medium_motivation = [
            "tired landlord", "downsizing", "upgrading", "moving",
            "retirement", "health issues", "financial hardship"
        ]

        # Low motivation indicators
        low_motivation = [
            "just curious", "testing market", "might sell", "maybe",
            "not sure", "thinking about it"
        ]

        score = 50  # Base score

        for indicator in high_motivation:
            if indicator in combined_text:
                score += 15

        for indicator in medium_motivation:
            if indicator in combined_text:
                score += 8

        for indicator in low_motivation:
            if indicator in combined_text:
                score -= 20

        return max(0, min(100, score))

    def _calculate_timeline_score(self) -> float:
        """Calculate timeline urgency score"""
        timeline_text = self.lead_data.get("timeline", "").lower()

        if any(word in timeline_text for word in ["asap", "immediately", "urgent", "this week"]):
            return 100
        elif any(word in timeline_text for word in ["soon", "next month", "30 days"]):
            return 80
        elif any(word in timeline_text for word in ["few months", "3 months", "90 days"]):
            return 60
        elif any(word in timeline_text for word in ["6 months", "next year"]):
            return 40
        else:
            return 50  # Default if no timeline specified

    def _calculate_condition_score(self) -> float:
        """Calculate property condition score"""
        condition_text = self.lead_data.get("condition", "").lower()

        if any(word in condition_text for word in ["excellent", "move-in ready", "updated"]):
            return 90
        elif any(word in condition_text for word in ["good", "minor repairs"]):
            return 70
        elif any(word in condition_text for word in ["fair", "needs work", "cosmetic"]):
            return 50
        elif any(word in condition_text for word in ["poor", "major repairs", "fixer"]):
            return 30
        else:
            return 60  # Default if no condition specified

    def _calculate_location_score(self) -> float:
        """Calculate location desirability score"""
        # This would integrate with market data APIs in production
        zip_code = self.lead_data.get("zip_code", "")
        city = self.lead_data.get("city", "").lower()

        # Placeholder scoring - would use real market data
        if city in ["atlanta", "nashville", "charlotte", "raleigh"]:
            return 90
        elif city in ["birmingham", "knoxville", "chattanooga"]:
            return 70
        else:
            return 60  # Default score

    def _calculate_confidence(self) -> float:
        """Calculate confidence score based on data completeness"""
        required_fields = [
            "property_value", "equity", "motivation", "condition",
            "timeline", "first_name", "phone", "email"
        ]

        completed_fields = sum(1 for field in required_fields
                             if self.lead_data.get(field))

        return (completed_fields / len(required_fields)) * 100

    def _generate_recommendations(self, total_score: float, factors: Dict[str, float]) -> List[str]:
        """Generate recommendations based on scoring factors"""
        recommendations = []

        if factors.get("property_value", 0) < 50:
            recommendations.append("Verify property value with recent comps")

        if factors.get("equity_amount", 0) < 60:
            recommendations.append("Confirm equity amount and outstanding liens")

        if factors.get("motivation_level", 0) < 70:
            recommendations.append("Probe deeper into seller motivation and timeline")

        if factors.get("property_condition", 0) < 50:
            recommendations.append("Schedule property inspection or request photos")

        if total_score >= 80:
            recommendations.append("Fast-track this lead for immediate processing")
        elif total_score >= 60:
            recommendations.append("Process within 24 hours")
        else:
            recommendations.append("Add to nurture sequence")

        return recommendations

    def _generate_next_actions(self, tier: int, priority: LeadPriority, score: float) -> List[str]:
        """Generate next actions based on tier and priority"""
        actions = []

        if tier == 1:
            actions.extend([
                "Run property comping analysis",
                "Calculate MAO (Maximum Allowable Offer)",
                "Generate initial offer"
            ])

            if priority in [LeadPriority.CRITICAL, LeadPriority.HIGH]:
                actions.append("Schedule follow-up within 2 hours")
            else:
                actions.append("Schedule follow-up within 24 hours")
        else:
            actions.extend([
                "Add to nurture campaign",
                "Schedule follow-up in 3-5 days",
                "Monitor for status changes"
            ])

        return actions

    def qualify_lead(self) -> QualificationResult:
        """
        Perform detailed lead qualification

        Returns:
            QualificationResult: Qualification analysis result
        """
        qualified = True
        qualification_score = 100.0
        disqualification_reasons = []
        qualification_factors = {}

        # Check minimum equity requirement
        equity = self._extract_numeric_value(self.lead_data.get("equity", 0))
        if equity < self.qualification_criteria["min_equity"]:
            qualified = False
            qualification_score -= 30
            disqualification_reasons.append(f"Insufficient equity: ${equity:,.0f} < ${self.qualification_criteria['min_equity']:,.0f}")

        qualification_factors["equity_check"] = equity >= self.qualification_criteria["min_equity"]

        # Check property value range
        property_value = self._extract_numeric_value(self.lead_data.get("property_value", 0))
        if property_value > 0:
            if property_value < self.qualification_criteria["min_property_value"]:
                qualified = False
                qualification_score -= 25
                disqualification_reasons.append(f"Property value too low: ${property_value:,.0f}")
            elif property_value > self.qualification_criteria["max_property_value"]:
                qualification_score -= 15  # Not disqualifying but reduces score
                disqualification_reasons.append(f"Property value high: ${property_value:,.0f} (requires special handling)")

        qualification_factors["value_range_check"] = (
            self.qualification_criteria["min_property_value"] <= property_value <= self.qualification_criteria["max_property_value"]
        )

        # Check for motivation indicators
        text_fields = [
            self.lead_data.get("motivation", ""),
            self.lead_data.get("situation", ""),
            self.lead_data.get("notes", ""),
            self.lead_data.get("comments", "")
        ]
        combined_text = " ".join(text_fields).lower()

        found_motivation = False
        for indicator in self.qualification_criteria["required_motivation_indicators"]:
            if indicator in combined_text:
                found_motivation = True
                break

        if not found_motivation:
            qualification_score -= 20
            disqualification_reasons.append("No clear motivation indicators found")

        qualification_factors["motivation_check"] = found_motivation

        # Check for disqualifying factors
        disqualifying_found = []
        for factor in self.qualification_criteria["disqualifying_factors"]:
            if factor in combined_text:
                disqualifying_found.append(factor)

        if disqualifying_found:
            qualified = False
            qualification_score -= 40
            disqualification_reasons.append(f"Disqualifying factors: {', '.join(disqualifying_found)}")

        qualification_factors["disqualifying_factors"] = disqualifying_found

        # Generate recommended actions
        recommended_actions = []
        if qualified:
            recommended_actions.extend([
                "Proceed with property analysis",
                "Schedule seller interview",
                "Begin comping process"
            ])
        else:
            recommended_actions.extend([
                "Add to nurture sequence",
                "Request additional information",
                "Monitor for status changes"
            ])

        return QualificationResult(
            qualified=qualified,
            qualification_score=max(0, qualification_score),
            disqualification_reasons=disqualification_reasons,
            qualification_factors=qualification_factors,
            recommended_actions=recommended_actions
        )

    def create_follow_up_sequence(self, lead_score: LeadScore) -> List[Dict[str, Any]]:
        """
        Create personalized follow-up sequence based on lead score

        Args:
            lead_score: Lead scoring result

        Returns:
            List of follow-up actions with timing and content
        """
        sequence = []

        if lead_score.priority == LeadPriority.CRITICAL:
            # Immediate and aggressive follow-up
            sequence = [
                {
                    "delay_hours": 0.5,
                    "type": "phone_call",
                    "message": "Immediate phone call - high priority lead",
                    "priority": "critical"
                },
                {
                    "delay_hours": 2,
                    "type": "sms",
                    "message": "Follow-up text if no answer",
                    "priority": "high"
                },
                {
                    "delay_hours": 24,
                    "type": "email",
                    "message": "Detailed email with offer preview",
                    "priority": "high"
                }
            ]
        elif lead_score.priority == LeadPriority.HIGH:
            # Standard high-priority follow-up
            sequence = [
                {
                    "delay_hours": 2,
                    "type": "phone_call",
                    "message": "Initial contact call",
                    "priority": "high"
                },
                {
                    "delay_hours": 24,
                    "type": "sms",
                    "message": "Follow-up text message",
                    "priority": "medium"
                },
                {
                    "delay_hours": 72,
                    "type": "email",
                    "message": "Detailed follow-up email",
                    "priority": "medium"
                }
            ]
        else:
            # Standard nurture sequence
            sequence = [
                {
                    "delay_hours": 24,
                    "type": "email",
                    "message": "Welcome email with information",
                    "priority": "medium"
                },
                {
                    "delay_hours": 120,  # 5 days
                    "type": "sms",
                    "message": "Check-in text message",
                    "priority": "low"
                },
                {
                    "delay_hours": 240,  # 10 days
                    "type": "email",
                    "message": "Market update email",
                    "priority": "low"
                }
            ]

        return sequence

    def run(self) -> Dict[str, Any]:
        """
        Run the enhanced lead processing workflow

        Returns:
            Dict containing processing results
        """
        try:
            logger.info(f"Starting enhanced lead processing for lead {self.lead_id}")

            # Step 1: Calculate advanced lead score
            lead_score = self.calculate_advanced_score()
            logger.info(f"Lead score calculated: {lead_score.total_score:.1f} (Tier {lead_score.tier}, {lead_score.priority.value})")

            # Step 2: Perform qualification
            qualification = self.qualify_lead()
            logger.info(f"Lead qualification: {'QUALIFIED' if qualification.qualified else 'NOT QUALIFIED'} (Score: {qualification.qualification_score:.1f})")

            # Step 3: Create follow-up sequence
            follow_up_sequence = self.create_follow_up_sequence(lead_score)
            logger.info(f"Created follow-up sequence with {len(follow_up_sequence)} actions")

            # Step 4: Update lead status and metadata
            self._update_lead_metadata(lead_score, qualification)

            # Step 5: Trigger next actions based on results
            next_actions_triggered = self._trigger_next_actions(lead_score, qualification)

            return {
                "success": True,
                "lead_id": self.lead_id,
                "lead_score": {
                    "total_score": lead_score.total_score,
                    "tier": lead_score.tier,
                    "priority": lead_score.priority.value,
                    "confidence": lead_score.confidence,
                    "factors": lead_score.factors,
                    "recommendations": lead_score.recommendations,
                    "next_actions": lead_score.next_actions
                },
                "qualification": {
                    "qualified": qualification.qualified,
                    "score": qualification.qualification_score,
                    "reasons": qualification.disqualification_reasons,
                    "factors": qualification.qualification_factors,
                    "recommended_actions": qualification.recommended_actions
                },
                "follow_up_sequence": follow_up_sequence,
                "actions_triggered": next_actions_triggered,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in enhanced lead processing: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "lead_id": self.lead_id,
                "timestamp": datetime.utcnow().isoformat()
            }

    def _update_lead_metadata(self, lead_score: LeadScore, qualification: QualificationResult):
        """Update lead metadata in the database"""
        try:
            # This would update the lead record in Supabase
            # For now, we'll log the updates that would be made
            logger.info(f"Would update lead {self.lead_id} with:")
            logger.info(f"  - Score: {lead_score.total_score}")
            logger.info(f"  - Tier: {lead_score.tier}")
            logger.info(f"  - Priority: {lead_score.priority.value}")
            logger.info(f"  - Qualified: {qualification.qualified}")
            logger.info(f"  - Status: {LeadStatus.QUALIFIED.value if qualification.qualified else LeadStatus.NURTURING.value}")
        except Exception as e:
            logger.error(f"Error updating lead metadata: {str(e)}")

    def _trigger_next_actions(self, lead_score: LeadScore, qualification: QualificationResult) -> List[str]:
        """Trigger next actions based on processing results"""
        triggered_actions = []

        try:
            if qualification.qualified and lead_score.tier == 1:
                # Trigger comping workflow
                logger.info(f"Triggering comping workflow for lead {self.lead_id}")
                triggered_actions.append("comping_workflow")

                # Trigger MAO calculation
                logger.info(f"Triggering MAO calculation for lead {self.lead_id}")
                triggered_actions.append("mao_calculation")

                if lead_score.priority in [LeadPriority.CRITICAL, LeadPriority.HIGH]:
                    # Trigger immediate follow-up
                    logger.info(f"Triggering immediate follow-up for lead {self.lead_id}")
                    triggered_actions.append("immediate_followup")
            else:
                # Add to nurture sequence
                logger.info(f"Adding lead {self.lead_id} to nurture sequence")
                triggered_actions.append("nurture_sequence")

        except Exception as e:
            logger.error(f"Error triggering next actions: {str(e)}")

        return triggered_actions

    @staticmethod
    def _extract_numeric_value(value) -> float:
        """Extract numeric value from string or number"""
        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            try:
                # Remove currency symbols and commas
                cleaned_value = value.replace('$', '').replace(',', '').strip()
                return float(cleaned_value)
            except ValueError:
                return 0.0

        return 0.0


# Convenience functions
def process_lead_enhanced(lead_id: str) -> Dict[str, Any]:
    """
    Process a lead with enhanced automation

    Args:
        lead_id: The ID of the lead to process

    Returns:
        Dict containing processing results
    """
    processor = EnhancedLeadProcessor(lead_id=lead_id)
    return processor.run()


def batch_process_leads(lead_ids: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Process multiple leads with enhanced automation

    Args:
        lead_ids: List of lead IDs to process

    Returns:
        Dict mapping lead IDs to their processing results
    """
    results = {}

    for lead_id in lead_ids:
        try:
            results[lead_id] = process_lead_enhanced(lead_id)
        except Exception as e:
            logger.error(f"Error processing lead {lead_id}: {str(e)}")
            results[lead_id] = {
                "success": False,
                "error": str(e),
                "lead_id": lead_id,
                "timestamp": datetime.utcnow().isoformat()
            }

    return results
