{"name": "GHL Lead → Supabase", "nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300], "parameters": {"httpMethod": "POST", "path": "ghl/lead", "responseMode": "onReceived", "responseData": "all"}}, {"name": "Postgres", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [600, 300], "parameters": {"operation": "insert", "table": "leads", "columns": ["id", "name", "email", "source"], "values": ["={{$json[\"id\"]}}", "={{$json[\"name\"]}}", "={{$json[\"email\"]}}", "\"GHL\""]}, "credentials": {"postgres": {"name": "Postgres account"}}}], "connections": {"Webhook": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "active": false}