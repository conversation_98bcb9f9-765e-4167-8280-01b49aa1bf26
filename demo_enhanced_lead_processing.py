#!/usr/bin/env python3
"""
Demo script for Enhanced Lead Processing System

This script demonstrates the enhanced lead processing capabilities including:
- Advanced lead scoring
- Intelligent qualification
- Follow-up automation
- Workflow integration
"""

import json
from datetime import datetime

def demo_lead_scoring():
    """Demonstrate advanced lead scoring"""
    print("🎯 Enhanced Lead Scoring Demo")
    print("=" * 40)
    
    # Sample lead data
    sample_leads = [
        {
            "name": "High-Value Lead",
            "data": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "property_value": 300000,
                "equity": 100000,
                "motivation": "foreclosure, urgent",
                "timeline": "immediately",
                "condition": "good condition",
                "city": "atlanta"
            }
        },
        {
            "name": "Medium-Value Lead", 
            "data": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "property_value": 200000,
                "equity": 60000,
                "motivation": "relocation",
                "timeline": "30 days",
                "condition": "needs minor repairs",
                "city": "nashville"
            }
        },
        {
            "name": "Low-Value Lead",
            "data": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "property_value": 80000,
                "equity": 10000,
                "motivation": "just curious",
                "timeline": "maybe next year",
                "condition": "poor condition",
                "city": "small_town"
            }
        }
    ]
    
    for lead in sample_leads:
        print(f"\n📋 {lead['name']}:")
        data = lead['data']
        
        # Calculate scores manually for demo
        property_score = calculate_property_score(data.get('property_value', 0))
        equity_score = calculate_equity_score(data.get('equity', 0))
        motivation_score = calculate_motivation_score(data.get('motivation', ''))
        timeline_score = calculate_timeline_score(data.get('timeline', ''))
        condition_score = calculate_condition_score(data.get('condition', ''))
        location_score = calculate_location_score(data.get('city', ''))
        
        # Weighted total score
        total_score = (
            property_score * 0.25 +
            equity_score * 0.20 +
            motivation_score * 0.20 +
            timeline_score * 0.15 +
            condition_score * 0.10 +
            location_score * 0.10
        )
        
        # Determine tier and priority
        tier = 1 if total_score >= 70 else 2
        if total_score >= 85:
            priority = "CRITICAL"
        elif total_score >= 70:
            priority = "HIGH"
        elif total_score >= 50:
            priority = "MEDIUM"
        else:
            priority = "LOW"
        
        print(f"   💰 Property Value: ${data.get('property_value', 0):,} (Score: {property_score:.1f})")
        print(f"   💎 Equity: ${data.get('equity', 0):,} (Score: {equity_score:.1f})")
        print(f"   🎯 Motivation: {data.get('motivation', 'N/A')} (Score: {motivation_score:.1f})")
        print(f"   ⏰ Timeline: {data.get('timeline', 'N/A')} (Score: {timeline_score:.1f})")
        print(f"   🏠 Condition: {data.get('condition', 'N/A')} (Score: {condition_score:.1f})")
        print(f"   📍 Location: {data.get('city', 'N/A')} (Score: {location_score:.1f})")
        print(f"   📊 TOTAL SCORE: {total_score:.1f}/100")
        print(f"   🏆 TIER: {tier} | PRIORITY: {priority}")

def calculate_property_score(value):
    """Calculate property value score"""
    if 150000 <= value <= 400000:
        return 100
    elif value < 150000:
        return max(0, (value / 150000) * 80)
    else:
        return max(20, 100 - ((value - 400000) / 10000))

def calculate_equity_score(equity):
    """Calculate equity score"""
    if equity >= 100000:
        return 100
    elif equity >= 50000:
        return 80
    elif equity >= 25000:
        return 60
    elif equity > 0:
        return 40
    else:
        return 0

def calculate_motivation_score(motivation):
    """Calculate motivation score"""
    motivation = motivation.lower()
    score = 50
    
    high_motivation = ["foreclosure", "divorce", "bankruptcy", "urgent", "immediately"]
    medium_motivation = ["relocation", "downsizing", "tired landlord"]
    low_motivation = ["curious", "maybe", "thinking"]
    
    for indicator in high_motivation:
        if indicator in motivation:
            score += 15
    
    for indicator in medium_motivation:
        if indicator in motivation:
            score += 8
    
    for indicator in low_motivation:
        if indicator in motivation:
            score -= 20
    
    return max(0, min(100, score))

def calculate_timeline_score(timeline):
    """Calculate timeline urgency score"""
    timeline = timeline.lower()
    
    if any(word in timeline for word in ["immediately", "urgent", "asap"]):
        return 100
    elif any(word in timeline for word in ["30 days", "soon"]):
        return 80
    elif any(word in timeline for word in ["few months", "90 days"]):
        return 60
    elif any(word in timeline for word in ["6 months", "next year"]):
        return 40
    else:
        return 50

def calculate_condition_score(condition):
    """Calculate property condition score"""
    condition = condition.lower()
    
    if any(word in condition for word in ["excellent", "good", "updated"]):
        return 90
    elif any(word in condition for word in ["minor repairs", "cosmetic"]):
        return 70
    elif any(word in condition for word in ["needs work", "fair"]):
        return 50
    elif any(word in condition for word in ["poor", "major repairs"]):
        return 30
    else:
        return 60

def calculate_location_score(city):
    """Calculate location desirability score"""
    city = city.lower()
    
    if city in ["atlanta", "nashville", "charlotte", "raleigh"]:
        return 90
    elif city in ["birmingham", "knoxville", "chattanooga"]:
        return 70
    else:
        return 60

def demo_qualification():
    """Demonstrate lead qualification"""
    print("\n\n🔍 Lead Qualification Demo")
    print("=" * 40)
    
    qualification_criteria = {
        "min_equity": 50000,
        "min_property_value": 100000,
        "max_property_value": 500000
    }
    
    test_leads = [
        {
            "name": "Qualified Lead",
            "equity": 75000,
            "property_value": 250000,
            "motivation": "foreclosure"
        },
        {
            "name": "Low Equity Lead",
            "equity": 25000,
            "property_value": 200000,
            "motivation": "relocation"
        },
        {
            "name": "No Motivation Lead",
            "equity": 80000,
            "property_value": 300000,
            "motivation": "just curious"
        }
    ]
    
    for lead in test_leads:
        print(f"\n📋 {lead['name']}:")
        
        qualified = True
        reasons = []
        score = 100
        
        # Check equity
        if lead['equity'] < qualification_criteria['min_equity']:
            qualified = False
            score -= 30
            reasons.append(f"Insufficient equity: ${lead['equity']:,}")
        
        # Check property value
        if lead['property_value'] < qualification_criteria['min_property_value']:
            qualified = False
            score -= 25
            reasons.append(f"Property value too low: ${lead['property_value']:,}")
        elif lead['property_value'] > qualification_criteria['max_property_value']:
            score -= 15
            reasons.append(f"High property value: ${lead['property_value']:,}")
        
        # Check motivation
        if "curious" in lead['motivation'].lower():
            qualified = False
            score -= 40
            reasons.append("Poor motivation indicators")
        
        print(f"   💰 Equity: ${lead['equity']:,}")
        print(f"   🏠 Property Value: ${lead['property_value']:,}")
        print(f"   🎯 Motivation: {lead['motivation']}")
        print(f"   ✅ QUALIFIED: {'YES' if qualified else 'NO'}")
        print(f"   📊 Score: {max(0, score):.1f}/100")
        if reasons:
            print(f"   ⚠️  Issues: {', '.join(reasons)}")

def demo_followup_sequences():
    """Demonstrate follow-up sequence creation"""
    print("\n\n📞 Follow-up Sequence Demo")
    print("=" * 40)
    
    sequences = {
        "Tier 1 High Priority": [
            {"delay": "30 minutes", "channel": "Phone", "message": "Immediate call attempt"},
            {"delay": "2 hours", "channel": "SMS", "message": "Urgent follow-up text"},
            {"delay": "6 hours", "channel": "Email", "message": "Detailed information request"},
            {"delay": "24 hours", "channel": "SMS", "message": "Interest confirmation"}
        ],
        "Tier 1 Standard": [
            {"delay": "2 hours", "channel": "SMS", "message": "Initial contact text"},
            {"delay": "6 hours", "channel": "Email", "message": "Welcome email"},
            {"delay": "24 hours", "channel": "Phone", "message": "Information gathering call"},
            {"delay": "72 hours", "channel": "SMS", "message": "Interest confirmation"}
        ],
        "Tier 2 Nurture": [
            {"delay": "24 hours", "channel": "Email", "message": "Welcome to nurture sequence"},
            {"delay": "5 days", "channel": "SMS", "message": "Check-in message"},
            {"delay": "10 days", "channel": "Email", "message": "Market update"},
            {"delay": "30 days", "channel": "Email", "message": "Long-term nurture"}
        ]
    }
    
    for sequence_name, actions in sequences.items():
        print(f"\n📋 {sequence_name}:")
        for i, action in enumerate(actions, 1):
            print(f"   {i}. {action['delay']} → {action['channel']}: {action['message']}")

def demo_workflow_integration():
    """Demonstrate workflow integration"""
    print("\n\n🔄 Workflow Integration Demo")
    print("=" * 40)
    
    workflow_example = {
        "lead_id": "demo_lead_123",
        "processing_steps": [
            "1. GHL Webhook Received",
            "2. Lead Data Extracted & Validated",
            "3. Lead Stored in Supabase",
            "4. Enhanced Lead Scoring (Score: 85.2)",
            "5. Lead Qualification (QUALIFIED)",
            "6. Tier 1 Classification",
            "7. Comping Workflow Triggered",
            "8. MAO Calculation Initiated",
            "9. High-Priority Follow-up Sequence Created",
            "10. Immediate Phone Call Scheduled"
        ],
        "results": {
            "score": 85.2,
            "tier": 1,
            "priority": "HIGH",
            "qualified": True,
            "next_actions": [
                "Property comping analysis",
                "MAO calculation",
                "Immediate follow-up"
            ]
        }
    }
    
    print("📊 Complete Lead Processing Workflow:")
    for step in workflow_example["processing_steps"]:
        print(f"   {step}")
    
    print(f"\n📋 Final Results:")
    results = workflow_example["results"]
    print(f"   📊 Score: {results['score']}/100")
    print(f"   🏆 Tier: {results['tier']}")
    print(f"   🚨 Priority: {results['priority']}")
    print(f"   ✅ Qualified: {results['qualified']}")
    print(f"   🎯 Next Actions: {', '.join(results['next_actions'])}")

def main():
    """Run the complete demo"""
    print("🚀 Enhanced Lead Processing System Demo")
    print("=" * 50)
    print(f"Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    demo_lead_scoring()
    demo_qualification()
    demo_followup_sequences()
    demo_workflow_integration()
    
    print("\n\n✅ Demo Complete!")
    print("=" * 50)
    print("🔧 Key Features Demonstrated:")
    print("   ✅ Advanced Lead Scoring (6 weighted factors)")
    print("   ✅ Intelligent Lead Qualification")
    print("   ✅ Personalized Follow-up Sequences")
    print("   ✅ Complete Workflow Integration")
    print("   ✅ Real-time Processing Pipeline")
    print("\n📋 Next Steps:")
    print("   1. Deploy the enhanced system: ./scripts/deploy_enhanced_system.sh")
    print("   2. Configure GHL webhooks")
    print("   3. Test with real lead data")
    print("   4. Monitor and optimize performance")

if __name__ == "__main__":
    main()
