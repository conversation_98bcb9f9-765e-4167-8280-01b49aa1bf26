version: '3.8'

services:
  # PostgreSQL database for n8n
  postgres:
    image: postgres:15
    container_name: ai-os-postgres
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8npassword
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5

  # n8n workflow automation
  n8n:
    image: n8nio/n8n:latest
    container_name: ai-os-n8n
    ports:
      - "5678:5678"
    env_file:
      - .env
    environment:
      DB_TYPE: "postgresdb"
      DB_POSTGRESDB_HOST: "postgres"
      DB_POSTGRESDB_PORT: "5432"
      DB_POSTGRESDB_DATABASE: "n8n"
      DB_POSTGRESDB_USER: "n8n"
      DB_POSTGRESDB_PASSWORD: "n8npassword"
      N8N_HOST: "0.0.0.0"
      N8N_PORT: "5678"
      N8N_PROTOCOL: "http"
      WEBHOOK_URL: "http://localhost:5678"
      GENERIC_TIMEZONE: "UTC"
      N8N_LOG_LEVEL: "info"
      N8N_METRICS: "true"
      N8N_API_KEY: "${N8N_API_KEY}"
    volumes:
      - n8n-data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flask API service
  api:
    build: .
    container_name: ai-os-api
    ports:
      - "5002:5002"
    environment:
      - PORT=5002
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook/ghl/lead
      - N8N_ALERT_WEBHOOK_URL=http://n8n:5678/webhook-test/util/ghl-alert
      - GHL_API_KEY=${GHL_API_KEY}
      - GHL_LOCATION_ID=${GHL_LOCATION_ID}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - RETOOL_API_KEY=${RETOOL_API_KEY}
      - RETOOL_ORG_ID=${RETOOL_ORG_ID}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
    volumes:
      - .:/app
    depends_on:
      - n8n
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5002/api/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres-data:
    driver: local
  n8n-data:
    driver: local

networks:
  aios_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
